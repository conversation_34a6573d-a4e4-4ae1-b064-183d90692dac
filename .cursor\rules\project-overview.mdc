---
description: 
globs: 
alwaysApply: false
---
# RooShop项目概述

RooShop是一个完整的电子商务平台，包含以下主要部分：

- **前端示例**：位于[Example/next-ecommerce-shopco](mdc:Example/next-ecommerce-shopco)目录，提供了基于Next.js的电商前端示例
- **后端API**：位于[RooshopAspNet](mdc:RooshopAspNet)目录，基于ASP.NET Core实现的完整电商后端
- **前端实现指南**：[RooShopWeb实现指南.md](mdc:RooShopWeb实现指南.md)提供了使用Turborepo、Next.js和shadcn/ui开发前端应用的指南
- **前端项目**：位于[RooshopWeb](mdc:RooshopWeb)目录，基于Turborepo Monorepo结构实现的电商前端，包含管理端和客户端应用

## 项目主要特点

- 前后端分离架构
- 后端使用ASP.NET Core实现RESTful API
- 前端已使用Turborepo Monorepo结构实现，包含管理端和客户端
- 支持多语言、多货币的电商平台
- 基于shadcn/ui的"零CSS编写"开发策略

## 前端详细设计

### 前端架构

RooshopWeb前端采用Turborepo Monorepo架构，主要包含：

1. **管理端应用** ([RooshopWeb/apps/admin](mdc:RooshopWeb/apps/admin))
   - 基于Next.js App Router
   - 使用shadcn/ui组件库和官方blocks
   - 实现产品管理、订单处理、客户管理、报表等功能
   - 支持国际化和权限控制

2. **客户端应用** ([RooshopWeb/apps/client](mdc:RooshopWeb/apps/client))
   - 基于Next.js App Router
   - 采用[Example/next-ecommerce-shopco](mdc:Example/next-ecommerce-shopco)设计
   - 提供产品浏览、购物车、结账流程等电商功能
   - 支持多语言和多货币

3. **共享基础设施** ([RooshopWeb/packages/core](mdc:RooshopWeb/packages/core))
   - API客户端：与后端RESTful API集成
   - 认证服务：处理JWT认证和用户会话
   - 国际化：基于next-intl的翻译管理
   - 工具函数：日期格式化、验证等通用功能

### 技术选择

- **UI策略**：管理端采用"零CSS编写"策略，使用shadcn/ui组件库和预制blocks
- **状态管理**：基于React Server Components和Client Components分离的状态管理
- **数据获取**：Server Components直接使用API客户端，Client Components使用SWR
- **国际化**：使用next-intl实现多语言支持
- **表单处理**：React Hook Form + Zod + shadcn/ui表单组件
- **表格处理**：shadcn/ui table + TanStack Table

### 开发策略

- **前端优先级**：优先开发管理端应用，提供完整的产品和订单管理功能
- **快速开发方法**：
  - 使用`pnpm dlx shadcn@latest add --all`一键安装所有UI组件
  - 利用官方blocks快速搭建完整页面（dashboard-01、sidebar-07等）
  - 注重功能实现和业务逻辑，减少UI定制时间
- **性能优化**：采用Next.js的静态生成、增量静态再生、代码分割等技术
- **质量保障**：配置ESLint、Typescript和自动化测试





