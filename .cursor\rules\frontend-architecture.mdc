---
description: 
globs: 
alwaysApply: false
---
# RooShop前端架构

RooShop前端采用Turborepo Monorepo结构，包含管理端和客户端两个应用，同时共享API集成和工具函数。

## 技术栈

- **项目结构**: Turborepo Monorepo
- **包管理工具**: pnpm (支持工作区)
- **前端框架**: Next.js (App Router)
- **UI组件库**: 
  - 管理端: shadcn/ui组件库 + 官方blocks
  - 客户端: 完全采用[Example/next-ecommerce-shopco](mdc:Example/next-ecommerce-shopco)设计
- **样式**: Tailwind CSS V4
- **状态管理**: React Server Components + Client Components分离策略
- **国际化**: next-intl

## 前端目录结构

```
RooshopWeb/                      # Turborepo根目录
├── apps/                         # 应用目录
│   ├── admin/                    # 管理端应用 (Next.js)
│   └── client/                   # 客户端应用 (Next.js)
├── packages/                     # 共享包目录
│   ├── core/                     # 核心服务与数据层
│   ├── eslint-config/            # 共享ESLint配置
│   └── typescript-config/        # 共享TypeScript配置
```

## 开发策略

- **"零CSS编写"策略**: 充分利用shadcn/ui提供的组件和blocks实现快速开发
- **管理端UI**: 使用shadcn/ui官方组件和预制blocks构建
- **客户端UI**: 完全采用next-ecommerce-shopco的设计
- **API集成**: 分层API集成策略，使用共享API客户端
- **表单处理**: React Hook Form + Zod + shadcn/ui表单组件的组合方案

详见[RooShopWeb实现指南.md](mdc:RooShopWeb实现指南.md)中的完整说明。

