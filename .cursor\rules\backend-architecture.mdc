---
description: 
globs: 
alwaysApply: false
---
# RooShop后端架构

RooShop后端采用基于ASP.NET Core的现代化架构，通过领域驱动设计(DDD)和清晰的层次结构实现可维护且可扩展的电商平台。

## 架构层次

- **应用层**: [Ecommerce.Application](mdc:RooshopAspNet/Ecommerce.Application)
  - 处理应用逻辑、用例实现和API契约
  - 包含命令(Commands)和查询(Queries)，按照CQRS模式组织
  - 提供各业务模块的DTOs和映射

- **领域层**: [Ecommerce.Domain](mdc:RooshopAspNet/Ecommerce.Domain)
  - 包含核心业务实体和业务规则
  - 定义聚合根、值对象和领域事件
  - 按照电商核心领域划分(订单、产品、用户等)

- **基础设施层**: [Ecommerce.Infrastructure](mdc:RooshopAspNet/Ecommerce.Infrastructure)
  - 提供技术实现细节(数据库访问、外部服务等)
  - 包含仓储模式实现、数据库配置和外部集成
  - 提供缓存、后台作业和监控功能

- **API层**: [Ecommerce.Web.Api](mdc:RooshopAspNet/Ecommerce.Web.Api)
  - 提供RESTful API端点
  - 实现API版本控制和文档
  - 处理请求验证和响应格式化

## 主要业务模块

系统包含以下核心业务模块:

1. **身份认证与授权**: 用户管理、角色和权限
2. **产品目录**: 产品、分类、价格管理
3. **购物车**: 购物车管理和结算流程
4. **订单管理**: 订单处理、状态跟踪
5. **库存管理**: 库存跟踪、警报和管理
6. **支付处理**: 多种支付方式集成
7. **商户管理**: 商户入驻和管理
8. **营销与促销**: 优惠券、折扣活动
9. **物流与配送**: 配送区域和费用计算
10. **评价与评论**: 产品评价系统
11. **退货与售后**: 退货处理和售后工单
12. **报表与数据**: 销售和库存报表

## 技术特点

- CQRS模式区分命令和查询职责
- 使用MediatR实现命令和查询处理
- 事件驱动设计用于跨聚合根通信
- 缓存策略提高性能
- 多语言支持
- 实现单元测试和自动化测试

