---
description: 
globs: 
alwaysApply: false
---
# RooShop性能优化指南

本规则文件描述RooShop项目的性能优化策略和最佳实践。

## 前端性能优化

根据[RooShopWeb实现指南.md](mdc:RooShopWeb实现指南.md)中的"构建性能优化"部分：

### Next.js优化

1. **构建配置优化**
   - 启用Turbopack加速开发体验
   - 配置构建缓存和依赖管理策略
   - 使用`output: 'standalone'`生成独立可执行的输出

2. **资源优化**
   - 配置图像自动优化和响应式加载
   - 实现字体子集化和优先级加载
   - 使用Next.js内置的图像和字体优化

3. **代码分割与懒加载**
   - 使用`next/dynamic`按需加载大型组件
   - 优化首屏加载核心模块的导入
   - 实现组件级懒加载策略

4. **渲染策略优化**
   - 为静态内容使用静态生成(SSG)
   - 为动态但不频繁变化的内容使用增量静态再生(ISR)
   - 为高度动态内容使用服务器组件
   - 为交互部分使用客户端组件

5. **体验优化**
   - 实现React Suspense边界的流式渲染
   - 优先加载关键路径CSS
   - 为首屏关键图像设置`priority`属性

### 性能监控

- 集成Core Web Vitals监控
- 为构建和部署流程配置性能预算检查
- 使用Lighthouse和Web Vitals库进行性能分析

## 后端性能优化

### 缓存策略

1. **多级缓存架构**
   - 内存缓存(IMemoryCache)用于高频访问数据
   - 分布式缓存(Redis)用于跨实例共享数据
   - 实体缓存策略(Entity Cache)用于领域实体

2. **缓存刷新机制**
   - 基于事件的缓存失效
   - 使用后台作业进行缓存预热
   - 配置缓存统计和监控

### 数据库优化

1. **查询优化**
   - 使用索引优化常见查询路径
   - 实现高效分页(避免OFFSET使用KEYSET)
   - 使用投影查询(仅选择所需字段)

2. **数据访问策略**
   - 使用EF Core的高级功能(查询投影、批量操作)
   - 为长查询实现异步流处理
   - 配置适当的命令和查询超时

### API性能优化

1. **响应压缩**
   - 启用GZIP/Brotli压缩
   - 配置缓存控制头
   - 实现ETags支持条件请求

2. **批处理API**
   - 提供批量操作端点减少往返
   - 实现高效的分页和筛选机制
   - 使用HTTP/2多路复用

3. **异步处理**
   - 将长时间运行任务移至后台作业
   - 实现基于事件的状态通知
   - 使用WebSocket进行实时更新

## 基础设施优化

1. **容器化优化**
   - 优化Docker镜像大小
   - 配置合适的资源限制和请求
   - 实现健康检查和弹性伸缩

2. **数据库伸缩**
   - 实现读写分离
   - 配置数据库连接池
   - 针对特定查询使用专用索引

3. **监控与警报**
   - 实现应用性能监控(APM)
   - 配置关键指标警报
   - 使用分布式追踪分析性能瓶颈

