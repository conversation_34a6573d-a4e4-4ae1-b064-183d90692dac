---
description: 
globs: 
alwaysApply: false
---
# RooShop开发工作流

本规则文件描述RooShop项目的开发工作流程和最佳实践。

## 前端开发工作流

前端开发遵循[RooShopWeb实现指南.md](mdc:RooShopWeb实现指南.md)中定义的工作流程和任务清单：

1. **项目初始化与结构设置**
   - 创建Turborepo项目和应用结构
   - 配置共享包和依赖管理
   - 设置UI组件库（shadcn/ui）

2. **共享基础设施开发**
   - 开发核心服务与数据层
   - 实现API客户端
   - 构建认证服务
   - 配置国际化功能

3. **管理端应用开发**
   - 实现基础布局和认证页面
   - 开发核心数据展示组件
   - 集成权限系统
   - 实现各业务模块

4. **客户端应用开发**
   - 采用next-ecommerce-shopco设计
   - 开发核心购物功能
   - 实现结账流程
   - 添加用户账户功能

5. **测试与部署准备**
   - 编写单元测试和E2E测试
   - 优化构建配置
   - 准备Docker文件
   - 配置Next.js优化

## 后端开发最佳实践

1. **领域驱动设计原则**
   - 按聚合根组织代码
   - 使用值对象表示无标识的概念
   - 通过领域事件实现聚合根之间的通信

2. **CQRS模式实践**
   - 命令(Commands)：修改状态的操作
   - 查询(Queries)：获取数据的操作
   - 使用专用DTOs和映射

3. **测试策略**
   - 编写单元测试覆盖核心业务逻辑
   - 使用集成测试验证基础设施集成
   - 自动化测试验证API端点

## 开发环境设置

1. **前端开发环境**
   ```bash
   # 克隆项目
   git clone 项目仓库地址
   
   # 安装依赖
   pnpm install
   
   # 运行开发服务器
   pnpm dev
   ```

2. **后端开发环境**
   ```bash
   # 运行PostgreSQL和Minio容器
   cd RooshopAspNet/Docker
   docker-compose up -d
   
   # 构建并运行API项目
   cd ..
   dotnet build
   dotnet run --project Ecommerce.Web.Api
   ```

## 项目共享规范

1. **代码风格**
   - 前端遵循ESLint和Prettier配置
   - 后端遵循.editorconfig和StyleCop规则

2. **提交规范**
   - 使用有意义的提交消息
   - 遵循功能分支工作流

3. **文档规范**
   - 更新README和实现指南
   - 为API端点添加注释
   - 更新数据库模式变更

