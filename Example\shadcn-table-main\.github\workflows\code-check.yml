name: Code check

on:
  pull_request:
    branches: ["*"]
  push:
    branches: ["main"]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

env:
  FORCE_COLOR: 3

jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.9.0
      - uses: ./.github/setup

      - name: Check formatting
        run: pnpm lint:fix

  lint:
    runs-on: ubuntu-latest
    name: Lint
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.9.0
      - uses: ./.github/setup

      - run: pnpm lint

  typecheck:
    runs-on: ubuntu-latest
    name: Typecheck
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.9.0
      - uses: ./.github/setup

      - run: pnpm typecheck
