{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "data-table-ts-doc", "type": "registry:file", "title": "Data Table TypeScript Doc", "description": "A TypeScript doc for the data table", "files": [{"path": "src/types/doc.ts", "content": "import type { DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\nimport type { EmptyProps } from \"@/types\";\nimport type { ExtendedColumnFilter, Option } from \"@/types/data-table\";\n\nimport type { Column, Table, TableOptions } from \"@tanstack/react-table\";\nimport type { motion } from \"motion/react\";\nimport type * as React from \"react\";\n\nexport interface UseDataTableProps<TData>\n  extends Required<Pick<TableOptions<TData>, \"pageCount\">>,\n    Pick<\n      TableOptions<TData>,\n      \"data\" | \"columns\" | \"getRowId\" | \"defaultColumn\" | \"initialState\"\n    > {\n  /**\n   * Determines how query updates affect history.\n   * `push` creates a new history entry; `replace` (default) updates the current entry.\n   * @default \"replace\"\n   */\n  history?: \"push\" | \"replace\";\n\n  /**\n   * Debounce time (ms) for filter updates to enhance performance during rapid input.\n   * @default 300\n   */\n  debounceMs?: number;\n\n  /**\n   * Maximum time (ms) to wait between URL query string updates.\n   * Helps with browser rate-limiting. Minimum effective value is 50ms.\n   * @default 50\n   */\n  throttleMs?: number;\n\n  /**\n   * Clear URL query key-value pair when state is set to default.\n   * Keep URL meaning consistent when defaults change.\n   * @default false\n   */\n  clearOnDefault?: boolean;\n\n  /**\n   * Enable notion like column filters.\n   * Advanced filters and column filters cannot be used at the same time.\n   * @default false\n   * @type boolean\n   */\n  enableAdvancedFilter?: boolean;\n\n  /**\n   * Whether the page should scroll to the top when the URL changes.\n   * @default false\n   */\n  scroll?: boolean;\n\n  /**\n   * Whether to keep query states client-side, avoiding server calls.\n   * Setting to `false` triggers a network request with the updated querystring.\n   * @default true\n   */\n  shallow?: boolean;\n\n  /**\n   * Observe Server Component loading states for non-shallow updates.\n   * Pass `startTransition` from `React.useTransition()`.\n   * Sets `shallow` to `false` automatically.\n   * So shallow: true` and `startTransition` cannot be used at the same time.\n   * @see https://react.dev/reference/react/useTransition\n   */\n  startTransition?: React.TransitionStartFunction;\n}\n\nexport interface DataTableProps<TData> extends EmptyProps<\"div\"> {\n  /** The table instance. */\n  table: Table<TData>;\n\n  /** The action bar to display above the table. */\n  actionBar?: React.ReactNode;\n}\n\nexport interface DataTableToolbarProps<TData> extends EmptyProps<\"div\"> {\n  /** The table instance. */\n  table: Table<TData>;\n}\n\nexport interface DataTableAdvancedToolbarProps<TData>\n  extends EmptyProps<\"div\"> {\n  /** The table instance. */\n  table: Table<TData>;\n}\n\nexport interface DataTableActionBarProps<TData>\n  extends EmptyProps<typeof motion.div> {\n  /** The table instance. */\n  table: Table<TData>;\n\n  /** Whether the action bar is visible. */\n  visible?: boolean;\n\n  /**\n   * The container to mount the portal into.\n   * @default document.body\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nexport interface DataTableColumnHeaderProps<TData, TValue>\n  extends EmptyProps<typeof DropdownMenuTrigger> {\n  /** The column instance. */\n  column: Column<TData, TValue>;\n\n  /** The column title. */\n  title: string;\n}\n\nexport interface DataTableDateFilterProps<TData> {\n  /** The column instance. */\n  column: Column<TData, unknown>;\n\n  /** The title of the date picker. */\n  title?: string;\n\n  /** Whether to enable range selection. */\n  multiple?: boolean;\n}\n\nexport interface DataTableFacetedFilterProps<TData, TValue> {\n  /** The column instance. */\n  column?: Column<TData, TValue>;\n\n  /** The title of the filter. */\n  title?: string;\n\n  /** The options of the filter. */\n  options: Option[];\n\n  /** Whether to enable multiple selection. */\n  multiple?: boolean;\n}\n\nexport interface DataTableSliderFilterProps<TData> {\n  /** The column instance. */\n  column: Column<TData, unknown>;\n\n  /** The title of the slider filter. */\n  title?: string;\n}\n\nexport interface DataTableRangeFilterProps<TData> extends EmptyProps<\"div\"> {\n  /** The extended column filter. */\n  filter: ExtendedColumnFilter<TData>;\n\n  /** The column instance. */\n  column: Column<TData>;\n\n  /** The input id for screen readers. */\n  inputId: string;\n\n  /** The function to update the filter. */\n  onFilterUpdate: (\n    filterId: string,\n    updates: Partial<Omit<ExtendedColumnFilter<TData>, \"filterId\">>,\n  ) => void;\n}\n\nexport interface DataTableFilterListProps<TData> {\n  /** The table instance. */\n  table: Table<TData>;\n\n  /**\n   * Debounce time (ms) for filter updates to enhance performance during rapid input.\n   * @default 300\n   */\n  debounceMs?: number;\n\n  /**\n   * Maximum time (ms) to wait between URL query string updates.\n   * Helps with browser rate-limiting. Minimum effective value is 50ms.\n   * @default 50\n   */\n  throttleMs?: number;\n\n  /**\n   * Whether to keep query states client-side, avoiding server calls.\n   * Setting to `false` triggers a network request with the updated querystring.\n   * @default true\n   */\n  shallow?: boolean;\n}\n\nexport interface DataTableFilterMenuProps<TData>\n  extends DataTableFilterListProps<TData> {}\n\nexport interface DataTableSortListProps<TData>\n  extends DataTableFilterListProps<TData> {}\n\nexport interface DataTablePaginationProps<TData> extends EmptyProps<\"div\"> {\n  /** The table instance. */\n  table: Table<TData>;\n\n  /**\n   * The options of the pagination.\n   * @default [10, 20, 30, 40, 50]\n   */\n  pageSizeOptions?: number[];\n}\n\nexport interface DataTableViewOptionsProps<TData> {\n  /** The table instance. */\n  table: Table<TData>;\n}\n\nexport interface DataTableSkeletonProps extends EmptyProps<\"div\"> {\n  /** The number of columns in the table. */\n  columnCount: number;\n\n  /**\n   * The number of rows in the table.\n   * @default 10\n   */\n  rowCount?: number;\n\n  /**\n   * The number of filters in the table.\n   * @default 0\n   */\n  filterCount?: number;\n\n  /**\n   * Array of CSS width values for each table column.\n   * The maximum length of the array must match columnCount, extra values will be ignored.\n   * @default [\"auto\"]\n   */\n  cellWidths?: string[];\n\n  /**\n   * Whether to show the view options.\n   * @default true\n   */\n  withViewOptions?: boolean;\n\n  /**\n   * Whether to show the pagination bar.\n   * @default true\n   */\n  withPagination?: boolean;\n\n  /**\n   * Whether to prevent the table cells from shrinking.\n   * @default false\n   */\n  shrinkZero?: boolean;\n}\n", "type": "registry:file", "target": "src/types/doc.ts"}]}